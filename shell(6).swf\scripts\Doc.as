package
{
   import com.junkbyte.console.Cc;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   
   public class Doc extends MovieClip
   {
      private var l:Loader;
      
      public var BagProxy:Class;
      
      public var GoodConfig:Class;
      
      public function Doc()
      {
         super();
         if(stage)
         {
            this.init();
         }
         else
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
      }
      
      private function init(param1:Event = null) : void
      {
         this.l = new Loader();
         this.l.contentLoaderInfo.addEventListener(Event.COMPLETE,this.l_complete);
         this.l.loadBytes(new MySWFClass());
      }
      
      private function l_complete(param1:Event) : void
      {
         this.addChild(this.l);
         Cc.startOnStage(this,"");
         Cc.visible = true;
         Cc.commandLine = true;
         Cc.config.commandLineAllowed = true;
         Cc.addSlashCommand("CreateGoods",this.create_goods,"刷道具");
         Cc.addSlashCommand("UnlockAllHeroes",this.unlock_all_heroes,"解锁所有角色");
         Cc.addSlashCommand("ExportGameIDs",this.export_game_ids,"导出游戏ID到桌面");
         Cc.addSlashCommand("AddAllCards",this.add_all_cards,"刷所有卡片");
         Cc.addSlashCommand("MaxAllHeroes",this.max_all_heroes,"所有角色满级");
         Cc.addSlashCommand("UnlockAllStages",this.unlock_all_stages,"解锁所有关卡");
         Cc.addSlashCommand("CompleteAllStages",this.complete_all_stages,"通关所有关卡");
         Cc.addSlashCommand("AddAllTitles",this.add_all_titles,"获得所有称号");
         Cc.addSlashCommand("CompleteTower",this.complete_tower,"一键通关镇妖塔");
         Cc.addSlashCommand("SetArenaRank",this.set_arena_rank,"设置竞技场排名");
         Cc.addSlashCommand("Music",this.sound_s,"静音");
      }
      
      private function create_goods(param1:String) : void
      {
         var _loc2_:Array = param1.split(",");
         var _loc3_:* = _loc2_[0];
         var _loc4_:* = _loc2_[1];
         this.BagProxy = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.bag.BagProxy") as Class;
         this.GoodConfig = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.GoodConfig") as Class;
         var _loc5_:* = this.GoodConfig.instance().newGood(_loc3_);
         _loc5_.amount.v = _loc4_;
         this.BagProxy.instance().addMulitItem(_loc5_);
      }
      
      public function sound_s() : void
      {
         var _loc1_:SoundTransform = new SoundTransform();
         _loc1_.volume = 0;
         SoundMixer.soundTransform = _loc1_;
      }
      
      private function unlock_all_heroes() : void
      {
         try
         {
            var HeroProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.role.HeroProxy") as Class;
            var heroProxy:* = HeroProxy.instance();
            
            // 解锁所有角色: 小白龙(1002), 猪八戒(1003), 沙僧(1004), 小龙女(1005)
            heroProxy.unlockHero(1002); // 小白龙
            heroProxy.unlockHero(1003); // 猪八戒  
            heroProxy.unlockHero(1004); // 沙僧
            heroProxy.unlockHero(1005); // 小龙女
            
            Cc.log("所有角色已解锁！");
            Cc.log("已解锁角色：孙悟空、小白龙、猪八戒、沙僧、小龙女");
         }
         catch(e:Error)
         {
            Cc.log("解锁角色失败: " + e.message);
         }
      }

      private function add_all_cards() : void
      {
         try
         {
            var CardProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.bag.CardProxy") as Class;
            var cardProxy:* = CardProxy.instance();

            Cc.log("开始刷所有卡片...");

            var cardCount:int = 0;
            var errorCount:int = 0;

            // 使用更高效的方法：直接从GoodConfig获取所有卡片
            var GoodConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.GoodConfig") as Class;
            var goodConfig:* = GoodConfig.instance();

            // 获取卡片类型的Dictionary
            if(goodConfig.hasOwnProperty("goodDic"))
            {
               var goodDic:* = goodConfig.goodDic;
               var cardTypeArray:Array = goodDic["7"] as Array; // 类型7是卡片

               if(cardTypeArray && cardTypeArray.length > 0)
               {
                  Cc.log("找到 " + cardTypeArray.length + " 种卡片，开始添加...");

                  for(var i:int = 0; i < cardTypeArray.length; i++)
                  {
                     try
                     {
                        var cardItem:* = cardTypeArray[i];
                        if(cardItem && cardItem.hasOwnProperty("id"))
                        {
                           var cardId:int = cardItem.id.v;
                           cardProxy.addCard(cardId, 1);
                           cardCount++;

                           // 每100个卡片显示一次进度
                           if(cardCount % 100 == 0)
                           {
                              Cc.log("已添加 " + cardCount + " 张卡片...");
                           }
                        }
                     }
                     catch(e:Error)
                     {
                        errorCount++;
                     }
                  }
               }
               else
               {
                  Cc.log("未找到卡片数据，使用备用方法...");
                  var result1:Object = this.addCardsByRange(cardProxy);
                  cardCount += result1.cardCount;
                  errorCount += result1.errorCount;
               }
            }
            else
            {
               Cc.log("无法访问goodDic，使用备用方法...");
               var result2:Object = this.addCardsByRange(cardProxy);
               cardCount += result2.cardCount;
               errorCount += result2.errorCount;
            }

            Cc.log("卡片刷取完成！");
            Cc.log("成功添加: " + cardCount + " 张卡片");
            if(errorCount > 0)
            {
               Cc.log("跳过无效ID: " + errorCount + " 个");
            }
            Cc.log("请打开卡片界面查看所有卡片！");
         }
         catch(e:Error)
         {
            Cc.log("刷卡片失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function addCardsByRange(cardProxy:*) : Object
      {
         // 备用方法：按ID范围添加卡片
         var GoodConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.GoodConfig") as Class;
         var goodConfig:* = GoodConfig.instance();

         var cardCount:int = 0;
         var errorCount:int = 0;

         // 1. 普通怪物卡片系列 (71001-71999)
         for(var i:int = 71001; i <= 71999; i++)
         {
            try
            {
               var good1:* = goodConfig.newGood(i);
               if(good1 && good1.constVO.type.v == "7") // 确认是卡片类型
               {
                  cardProxy.addCard(i, 1);
                  cardCount++;
               }
            }
            catch(e:Error)
            {
               errorCount++;
            }
         }

         // 2. BOSS卡片系列 (70001-70999)
         for(var j:int = 70001; j <= 70999; j++)
         {
            try
            {
               var good2:* = goodConfig.newGood(j);
               if(good2 && good2.constVO.type.v == "7") // 确认是卡片类型
               {
                  cardProxy.addCard(j, 1);
                  cardCount++;
               }
            }
            catch(e:Error)
            {
               errorCount++;
            }
         }

         return {cardCount: cardCount, errorCount: errorCount};
      }

      private function max_all_heroes() : void
      {
         try
         {
            var HeroProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.role.HeroProxy") as Class;
            var heroProxy:* = HeroProxy.instance();

            Cc.log("开始设置所有角色满级...");

            // 获取所有角色
            var allHeroes:Array = heroProxy.collectAllHero();
            var maxLevel:int = 53; // 从HeroGameVO.MAX_LEVEL得知最大等级是53
            var processedCount:int = 0;

            for(var i:int = 0; i < allHeroes.length; i++)
            {
               var hero:* = allHeroes[i];
               if(hero != null)
               {
                  var heroName:String = this.getHeroName(hero.id.v);
                  var oldLevel:int = hero.level.v;

                  // 设置角色等级为满级
                  hero.setLevel(maxLevel, true);

                  Cc.log(heroName + ": " + oldLevel + "级 → " + maxLevel + "级");
                  processedCount++;
               }
            }

            // 处理宠物满级
            var PetProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.pet.PetProxy") as Class;
            var petProxy:* = PetProxy.instance();
            var petList:Array = petProxy.petList;
            var petProcessedCount:int = 0;

            if(petList && petList.length > 0)
            {
               Cc.log("开始设置所有宠物满级...");

               for(var j:int = 0; j < petList.length; j++)
               {
                  var pet:* = petList[j];
                  if(pet != null)
                  {
                     var petName:String = this.getPetName(pet.id.v);
                     var oldPetLevel:int = pet.level.v;
                     var petMaxLevel:int = pet.curMaxLevel; // 根据进化等级获取最大等级

                     // 设置宠物等级为满级
                     pet.setLevel(petMaxLevel, true);

                     Cc.log(petName + ": " + oldPetLevel + "级 → " + petMaxLevel + "级");
                     petProcessedCount++;
                  }
               }
            }

            Cc.log("角色和宠物满级设置完成！");
            Cc.log("已处理 " + processedCount + " 个角色");
            Cc.log("已处理 " + petProcessedCount + " 个宠物");
            Cc.log("所有角色已达到满级 " + maxLevel + " 级！");
            if(petProcessedCount > 0)
            {
               Cc.log("所有宠物已达到各自的满级！");
            }
         }
         catch(e:Error)
         {
            Cc.log("设置角色满级失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function getHeroName(heroId:int) : String
      {
         switch(heroId)
         {
            case 1001:
               return "孙悟空";
            case 1002:
               return "小白龙";
            case 1003:
               return "猪八戒";
            case 1004:
               return "沙僧";
            case 1005:
               return "小龙女";
            default:
               return "未知角色(" + heroId + ")";
         }
      }

      private function getPetName(petId:int) : String
      {
         switch(petId)
         {
            case 1101:
               return "魔炎";
            case 1102:
               return "蛇灵";
            case 1103:
               return "金灵";
            case 1104:
               return "水灵";
            case 1106:
               return "冰麒麟";
            case 1107:
               return "火凤凰";
            default:
               return "未知宠物(" + petId + ")";
         }
      }

      private function unlock_all_stages() : void
      {
         try
         {
            var FlagProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.flag.FlagProxy") as Class;
            var flagProxy:* = FlagProxy.instance();

            Cc.log("开始解锁所有关卡...");

            var unlockedCount:int = 0;

            // 解锁主线关卡 (1001-1018)
            for(var i:int = 1001; i <= 1018; i++)
            {
               if(!flagProxy.isComplete(i))
               {
                  flagProxy.setValue(i, 1);
                  unlockedCount++;
               }
            }

            // 解锁地图进入条件
            var mapFlags:Array = [
               1009, // 吐蕃地图解锁条件
               1018, // 西域地图解锁条件
               1026, // 天竺地图解锁条件
               1033  // 妖岛解锁条件（大雄宝殿通关flag）
            ];

            for(var k:int = 0; k < mapFlags.length; k++)
            {
               if(!flagProxy.isComplete(mapFlags[k]))
               {
                  flagProxy.setValue(mapFlags[k], 1);
                  unlockedCount++;
               }
            }

            // 解锁天竺地图关卡
            var tianZhuStages:Array = [
               2700, // 阴雾山
               2800, // 豹头山
               2900, // 朱紫山
               3000, // 青龙山
               3100, // 不尽禅寺
               3200, // 雷音寺
               18    // 大雄宝殿
            ];

            for(var m:int = 0; m < tianZhuStages.length; m++)
            {
               if(!flagProxy.isComplete(tianZhuStages[m]))
               {
                  flagProxy.setValue(tianZhuStages[m], 1);
                  unlockedCount++;
               }
            }

            // 解锁妖岛地图关卡
            var yaoDaoStages:Array = [
               3300, // 阴山
               3400, // 獐鹅山
               3500, // 鹰山
               3600, // 牛羊山
               3700, // 土丘
               3800, // 迷林
               3900, // 三危山
               4000  // 獐鹿山
            ];

            for(var n:int = 0; n < yaoDaoStages.length; n++)
            {
               if(!flagProxy.isComplete(yaoDaoStages[n]))
               {
                  flagProxy.setValue(yaoDaoStages[n], 1);
                  unlockedCount++;
               }
            }

            // 解锁特殊关卡和副本
            var specialStages:Array = [
               2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, // 特殊关卡
               2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020,
               2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030,
               2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040,
               2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050,
               2051, 2052, 2053 // 天竺相关剧情flag
            ];

            for(var j:int = 0; j < specialStages.length; j++)
            {
               if(!flagProxy.isComplete(specialStages[j]))
               {
                  flagProxy.setValue(specialStages[j], 1);
                  unlockedCount++;
               }
            }

            Cc.log("关卡解锁完成！");
            Cc.log("已解锁 " + unlockedCount + " 个关卡");
            Cc.log("所有主线、天竺、妖岛和特殊关卡已解锁！");
            Cc.log("天竺地图和妖岛现在应该可以进入了！");
         }
         catch(e:Error)
         {
            Cc.log("解锁关卡失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function complete_all_stages() : void
      {
         try
         {
            var FlagProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.flag.FlagProxy") as Class;
            var flagProxy:* = FlagProxy.instance();

            Cc.log("开始通关所有关卡...");
            Cc.log("FlagProxy实例: " + (flagProxy ? "成功获取" : "获取失败"));

            var completedCount:int = 0;

            // 通关主线关卡 (1001-1018)
            for(var i:int = 1001; i <= 1018; i++)
            {
               flagProxy.setValue(i, 1);
               completedCount++;
            }

            // 通关地图解锁关键flag
            var mapFlags:Array = [
               1009, // 吐蕃地图解锁条件
               1018, // 西域地图解锁条件
               1026, // 天竺地图解锁条件
               1033  // 妖岛解锁条件（大雄宝殿通关flag）
            ];

            for(var k:int = 0; k < mapFlags.length; k++)
            {
               flagProxy.setValue(mapFlags[k], 1);
               completedCount++;
            }

            // 通关天竺地图关卡
            var tianZhuStages:Array = [
               2700, // 阴雾山
               2800, // 豹头山
               2900, // 朱紫山
               3000, // 青龙山
               3100, // 不尽禅寺
               3200, // 雷音寺
               18    // 大雄宝殿
            ];

            for(var m:int = 0; m < tianZhuStages.length; m++)
            {
               flagProxy.setValue(tianZhuStages[m], 1);
               completedCount++;
            }

            // 通关妖岛地图关卡
            var yaoDaoStages:Array = [
               3300, // 阴山
               3400, // 獐鹅山
               3500, // 鹰山
               3600, // 牛羊山
               3700, // 土丘
               3800, // 迷林
               3900, // 三危山
               4000  // 獐鹿山
            ];

            for(var n:int = 0; n < yaoDaoStages.length; n++)
            {
               flagProxy.setValue(yaoDaoStages[n], 1);
               completedCount++;
            }

            // 通关特殊关卡和剧情flag
            var allStages:Array = [
               2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010,
               2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020,
               2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030,
               2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040,
               2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050,
               2051, 2052, 2053 // 天竺相关剧情flag
            ];

            for(var j:int = 0; j < allStages.length; j++)
            {
               flagProxy.setValue(allStages[j], 1);
               completedCount++;
            }

            Cc.log("关卡通关完成！");
            Cc.log("已通关 " + completedCount + " 个关卡");
            Cc.log("所有主线、天竺、妖岛和特殊关卡已通关！");
            Cc.log("天竺地图和妖岛现在应该可以进入了！");
         }
         catch(e:Error)
         {
            Cc.log("通关关卡失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function add_all_titles() : void
      {
         try
         {
            var ChenghaoProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.chenghao.ChenghaoProxy") as Class;
            var chenghaoProxy:* = ChenghaoProxy.instance();

            Cc.log("开始获得所有称号...");
            Cc.log("ChenghaoProxy实例: " + (chenghaoProxy ? "成功获取" : "获取失败"));

            // 确保ChenghaoProxy已初始化
            chenghaoProxy.startNew();
            Cc.log("ChenghaoProxy已初始化");

            var titleCount:int = 0;

            // 直接使用称号ID列表
            var titleIds:Array = [
               65001, // 天妖新人
               65002, // 一路向西
               65003, // 东土勇士
               65004, // 吐蕃勇士
               65005, // 西域勇士
               65006, // 天竺勇士
               65007, // 任务达人
               65008, // 铜钱达人
               65009, // 土豪
               65010, // 国庆称号
               65011, // 登录称号
               65015, // 暑假称号
               65016, // 活动称号
               65017, // 限时称号
               65100, // 竞技第一
               65101, // 竞技第二
               65102, // 竞技第三
               65103, // 竞技前十
               65104  // 竞技前五十
            ];

            for(var i:int = 0; i < titleIds.length; i++)
            {
               var titleId:int = titleIds[i];

               // 使用ChenghaoProxy的setGet方法来正确设置称号
               chenghaoProxy.setGet(titleId);
               titleCount++;

               var titleName:String = this.getTitleName(titleId);
               Cc.log("获得称号: " + titleName + " (ID: " + titleId + ")");
            }

            Cc.log("称号获取完成！");
            Cc.log("已获得 " + titleCount + " 个称号");
            Cc.log("所有称号已解锁！");
         }
         catch(e:Error)
         {
            Cc.log("获取称号失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function getTitleName(titleId:int) : String
      {
         switch(titleId)
         {
            case 65001:
               return "灭妖新人";
            case 65002:
               return "一路向西";
            case 65003:
               return "东土勇士";
            case 65004:
               return "吐蕃勇士";
            case 65005:
               return "腰缠万贯";
            case 65006:
               return "任务达人";
            case 65007:
               return "暑假超级爽";
            case 65008:
               return "土豪";
            case 65009:
               return "中秋快乐";
            case 65010:
               return "西域勇士";
            case 65011:
               return "灭妖尊者";
            case 65015:
               return "国庆节快乐";
            case 65016:
               return "喜庆闹元宵";
            case 65017:
               return "灭妖周年庆";
            case 65100:
               return "唯我独尊";
            case 65101:
               return "威震寰宇";
            case 65102:
               return "无与伦比";
            case 65103:
               return "罕见敌手";
            case 65104:
               return "高手寂寞";
            default:
               return "未知称号(" + titleId + ")";
         }
      }

      private function complete_tower() : void
      {
         try
         {
            var TowerProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.tower.TowerProxy") as Class;
            var towerProxy:* = TowerProxy.instance();

            Cc.log("开始一键通关镇妖塔...");

            var completedCount:int = 0;

            // 镇妖塔所有boss ID列表
            var allBossIds:Array = [
               // 塔0层0 (8个boss)
               3002, 3003, 3004, 3010, 3005, 3006, 3013, 3014,
               // 塔0层1 (8个boss)
               4001, 4002, 4003, 4004, 3015, 3017, 3018, 3019,
               // 塔0层2 (8个boss)
               3020, 3021, 3023, 3024, 3025, 4005, 3026, 3027,
               // 塔1层0 (6个boss)
               4006, 4007, 4008, 3028, 3029, 3032,
               // 塔1层1 (6个boss)
               3034, 3035, 3037, 3038, 3042, 3043,
               // 塔1层2 (6个boss)
               3039, 3040, 3044, 3045, 3046, 3047
            ];

            // 击败所有boss
            for(var i:int = 0; i < allBossIds.length; i++)
            {
               var bossId:int = allBossIds[i];
               var bossVO:* = towerProxy.findTowerBoss(bossId);
               if(bossVO && !bossVO.hasGet)
               {
                  bossVO.setGet();
                  completedCount++;
                  Cc.log("击败boss: " + bossId);
               }
               else if(bossVO && bossVO.hasGet)
               {
                  Cc.log("boss " + bossId + " 已经被击败过了");
               }
               else
               {
                  Cc.log("未找到boss: " + bossId);
               }
            }

            Cc.log("镇妖塔通关完成！");
            Cc.log("本次击败了 " + completedCount + " 个新boss");
            Cc.log("所有镇妖塔层数已通关！");
         }
         catch(e:Error)
         {
            Cc.log("通关镇妖塔失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function set_arena_rank(param1:String) : void
      {
         try
         {
            var RolePKProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.pk.RolePKProxy") as Class;
            var MyRankVO:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.rank.vo.MyRankVO") as Class;
            var rolePKProxy:* = RolePKProxy.instance();

            var targetRank:int = parseInt(param1);
            if(isNaN(targetRank) || targetRank < 1)
            {
               Cc.log("请输入有效的排名数字（大于0）");
               return;
            }

            Cc.log("开始设置竞技场排名...");
            Cc.log("目标排名: " + targetRank);

            // 确保rankHandler已初始化
            if(!rolePKProxy.rankHandler)
            {
               Cc.log("竞技场排行榜未初始化，正在初始化...");
               var RankRolePK:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.rank.RankRolePK") as Class;
               rolePKProxy.rankHandler = new RankRolePK();
            }

            // 确保myRanks数组已初始化
            if(!rolePKProxy.rankHandler.myRanks)
            {
               rolePKProxy.rankHandler.myRanks = [];
            }

            // 查找或创建竞技场排名记录
            var myRankVO:* = null;
            var found:Boolean = false;

            for(var i:int = 0; i < rolePKProxy.rankHandler.myRanks.length; i++)
            {
               var rankVO:* = rolePKProxy.rankHandler.myRanks[i];
               if(rankVO && rankVO.rid == 1826) // 1826是竞技场排行榜ID
               {
                  myRankVO = rankVO;
                  found = true;
                  break;
               }
            }

            // 如果没找到，创建新的排名记录
            if(!found)
            {
               myRankVO = new MyRankVO();
               myRankVO.rid = 1826; // 竞技场排行榜ID
               rolePKProxy.rankHandler.myRanks.push(myRankVO);
            }

            // 设置排名
            var oldRank:int = myRankVO.rank.v;
            myRankVO.rank.v = targetRank;

            // 根据排名设置合适的积分（排名越高积分越高）
            var newScore:int;
            if(targetRank == 1)
            {
               newScore = 99999; // 第1名设置超高积分
            }
            else if(targetRank <= 10)
            {
               newScore = 10000 - targetRank * 100; // 前10名高积分
            }
            else
            {
               newScore = Math.max(100, 2000 - targetRank * 10); // 其他排名正常积分
            }
            rolePKProxy.pkVO.score.v = newScore;

            // 强制保存数据
            var PKGManager:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gamePKG.PKGManager") as Class;
            PKGManager.saveWithTip();

            Cc.log("竞技场排名设置完成！");
            Cc.log("排名: " + oldRank + " → " + targetRank);
            Cc.log("积分: " + newScore);
            Cc.log("数据已保存，请重新进入竞技场查看效果");

            if(targetRank == 1)
            {
               Cc.log("已设置为第1名，积分99999，应该能超过所有玩家！");
            }
         }
         catch(e:Error)
         {
            Cc.log("设置竞技场排名失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      // 设置综合排行榜排名
      public function SetRank(param1:String) : void
      {
         try
         {
            var params:Array = param1.split(",");
            if(params.length < 2)
            {
               Cc.log("用法: /SetRank 排行榜类型,目标排名");
               Cc.log("排行榜类型:");
               Cc.log("  总战力 - 设置总战力排行榜");
               Cc.log("  战力 - 设置角色战力排行榜");
               Cc.log("  生命 - 设置生命排行榜");
               Cc.log("  攻击 - 设置攻击排行榜");
               Cc.log("  物防 - 设置物防排行榜");
               Cc.log("  魔防 - 设置魔防排行榜");
               Cc.log("  宠物战力 - 设置宠物战力排行榜");
               Cc.log("例如: /SetRank 总战力,1");
               return;
            }

            var rankType:String = params[0];
            var targetRank:int = parseInt(params[1]);

            if(targetRank < 1 || targetRank > 10000)
            {
               Cc.log("排名必须在1-10000之间！");
               return;
            }

            this.submitCustomRankData(rankType, targetRank);
         }
         catch(e:Error)
         {
            Cc.log("设置排行榜失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      // 提交自定义排行榜数据
      private function submitCustomRankData(rankType:String, targetRank:int) : void
      {
         try
         {
            var Open4399Rank:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("unit4399.Open4399Rank") as Class;
            var PKGManager:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gamePKG.PKGManager") as Class;
            var HeroProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.role.HeroProxy") as Class;
            var PetProxy:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("mogames.gameData.pet.PetProxy") as Class;

            var heroProxy:Object = HeroProxy.instance();
            var petProxy:Object = PetProxy.instance();
            var rankData:Array = [];
            var rankIDs:Array = [];
            var scoreValue:int = this.calculateScoreByRank(targetRank);

            // 根据排行榜类型设置对应的数据
            switch(rankType)
            {
               case "总战力":
                  rankIDs = [1782];
                  rankData.push({
                     "rId": 1782,
                     "score": scoreValue,
                     "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue] // 5个角色的战力
                  });
                  break;

               case "战力":
                  rankIDs = [1702,1703,1704,1705,1706]; // 5个角色的战力排行榜ID
                  // 为每个角色创建排行榜数据
                  for(var i:int = 0; i < 5; i++)
                  {
                     rankData.push({
                        "rId": rankIDs[i],
                        "score": scoreValue,
                        "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue] // 生命,攻击,物防,魔防,战力
                     });
                  }
                  break;

               case "生命":
                  rankIDs = [1707,1708,1709,1710,1711];
                  for(var j:int = 0; j < 5; j++)
                  {
                     rankData.push({
                        "rId": rankIDs[j],
                        "score": scoreValue,
                        "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue]
                     });
                  }
                  break;

               case "攻击":
                  rankIDs = [1712,1713,1714,1715,1716];
                  for(var k:int = 0; k < 5; k++)
                  {
                     rankData.push({
                        "rId": rankIDs[k],
                        "score": scoreValue,
                        "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue]
                     });
                  }
                  break;

               case "物防":
                  rankIDs = [1717,1718,1719,1720,1721];
                  for(var l:int = 0; l < 5; l++)
                  {
                     rankData.push({
                        "rId": rankIDs[l],
                        "score": scoreValue,
                        "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue]
                     });
                  }
                  break;

               case "魔防":
                  rankIDs = [1722,1723,1724,1725,1726];
                  for(var m:int = 0; m < 5; m++)
                  {
                     rankData.push({
                        "rId": rankIDs[m],
                        "score": scoreValue,
                        "extra": [scoreValue, scoreValue, scoreValue, scoreValue, scoreValue]
                     });
                  }
                  break;

               case "宠物战力":
                  rankIDs = [1798,1809,1842,1866,1867,1896]; // 6个宠物的战力排行榜ID
                  for(var n:int = 0; n < 6; n++)
                  {
                     rankData.push({
                        "rId": rankIDs[n],
                        "score": scoreValue,
                        "extra": ["自定义宠物", 999, 999, 999, 999, 999, 999] // 宠物名,等级,成长,天赋,悟性,学习,品质
                     });
                  }
                  break;

               default:
                  Cc.log("不支持的排行榜类型: " + rankType);
                  return;
            }

            // 提交排行榜数据
            var submitFunc:Function = function(result:Array):void
            {
               Cc.log("排行榜数据提交完成！");
               Cc.log("类型: " + rankType);
               Cc.log("目标排名: " + targetRank);
               Cc.log("设置积分: " + scoreValue);
               Cc.log("请重新进入对应排行榜查看效果");

               if(result && result.length > 0)
               {
                  for(var i:int = 0; i < result.length; i++)
                  {
                     var item:Object = result[i];
                     if(item.code == "10000")
                     {
                        Cc.log("排行榜ID " + item.rId + " 当前排名: " + item.curRank);
                     }
                  }
               }
            };

            Cc.log("正在提交" + rankType + "排行榜数据...");
            Open4399Rank.submitScoreToRankLists(PKGManager.curIndex.v, rankData, submitFunc);
         }
         catch(e:Error)
         {
            Cc.log("提交排行榜数据失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      // 根据目标排名计算合适的积分
      private function calculateScoreByRank(targetRank:int) : int
      {
         if(targetRank == 1)
         {
            return 999999; // 第1名超高积分
         }
         else if(targetRank <= 10)
         {
            return 100000 - targetRank * 1000; // 前10名高积分
         }
         else if(targetRank <= 100)
         {
            return 50000 - targetRank * 100; // 前100名中等积分
         }
         else
         {
            return Math.max(1000, 10000 - targetRank * 10); // 其他排名正常积分
         }
      }

      private function export_game_ids() : void
      {
         try
         {
            var output:Array = [];
            output.push("=== 西游记游戏完整数据导出 ===");
            output.push("导出时间: " + new Date().toString());
            output.push("==================================================\n");

            // 动态获取所有配置类
            this.exportAllConfigs(output);

            var content:String = output.join("\n");

            // 使用FileReference保存到桌面
            var FileReference:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.net.FileReference") as Class;
            var file:* = new FileReference();
            file.save(content, "西游记完整游戏数据_" + new Date().getTime() + ".txt");

            Cc.log("完整游戏数据已导出到桌面！文件大小: " + Math.round(content.length / 1024) + "KB");
         }
         catch(e:Error)
         {
            Cc.log("导出失败: " + e.message);
            Cc.log("错误堆栈: " + e.getStackTrace());
         }
      }

      private function exportAllConfigs(output:Array) : void
      {
         try
         {
            // 导出道具配置
            this.exportGoodConfig(output);

            // 导出英雄配置
            this.exportHeroConfig(output);

            // 导出宠物配置
            this.exportPetConfig(output);

            // 导出技能配置
            this.exportSkillConfig(output);

            // 导出装备配置
            this.exportEquipConfig(output);

            // 导出任务配置
            this.exportMissionConfig(output);

            // 导出敌人配置
            this.exportEnemyConfig(output);

            // 导出商城配置
            this.exportMallConfig(output);

            // 导出其他配置
            this.exportOtherConfigs(output);

            output.push("\n=== 导出完成 ===");
            output.push("使用方法：");
            output.push("刷道具: /CreateGoods 道具ID,数量");
            output.push("例如: /CreateGoods 10033,999 (刷999个金锭)");
            output.push("解锁角色: /UnlockAllHeroes");
         }
         catch(e:Error)
         {
            output.push("配置导出错误: " + e.message);
         }
      }

      private function exportGoodConfig(output:Array) : void
      {
         try
         {
            var GoodConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.GoodConfig") as Class;
            var goodConfig:* = GoodConfig.instance();

            output.push("【道具配置 - GoodConfig】");

            // 先尝试调用初始化方法
            if(goodConfig.hasOwnProperty("init"))
            {
               try { goodConfig.init(); } catch(e:Error) { }
            }

            // 尝试多种可能的属性名，包括goodDic
            var foundData:Boolean = false;
            var possibleProps:Array = ["goodDic", "_goods", "goods", "_goodList", "goodList", "_items", "items"];

            for each(var propName:String in possibleProps)
            {
               if(goodConfig.hasOwnProperty(propName))
               {
                  var goods:* = goodConfig[propName];
                  if(goods)
                  {
                     output.push("找到属性: " + propName);
                     var count:int = 0;

                     // 特殊处理goodDic (Dictionary类型)
                     if(propName == "goodDic")
                     {
                        try
                        {
                           for(var type:* in goods)
                           {
                              var typeArray:Array = goods[type] as Array;
                              if(typeArray && typeArray.length > 0)
                              {
                                 output.push("  类型 " + type + " (" + typeArray.length + "个道具):");

                                 for(var j:int = 0; j < typeArray.length; j++)
                                 {
                                    try
                                    {
                                       var dictItem:* = typeArray[j];
                                       if(dictItem && dictItem.hasOwnProperty("id"))
                                       {
                                          var itemId:String = String(dictItem.id.v);
                                          var dictName:String = "未知";

                                          // 安全地获取名称
                                          if(dictItem.hasOwnProperty("name"))
                                          {
                                             if(dictItem.name.hasOwnProperty("v"))
                                             {
                                                dictName = dictItem.name.v;
                                             }
                                             else
                                             {
                                                dictName = String(dictItem.name);
                                             }
                                          }

                                          output.push("    " + itemId + " - " + dictName);
                                          count++;
                                       }
                                    }
                                    catch(itemError:Error)
                                    {
                                       output.push("    [" + j + "] 访问失败: " + itemError.message);
                                    }
                                 }


                              }
                           }
                        }
                        catch(dicError:Error)
                        {
                           output.push("  Dictionary遍历失败: " + dicError.message);
                        }
                     }
                     else if(goods is Array)
                     {
                        var arr:Array = goods as Array;
                        for(var i:int = 0; i < arr.length; i++)
                        {
                           var item:* = arr[i];
                           if(item && item.hasOwnProperty("id"))
                           {
                              var name:String = item.hasOwnProperty("name") ? (" - " + item.name.v) : "";
                              output.push("  " + item.id.v + name);
                              count++;
                           }
                        }
                     }
                     else
                     {
                        for(var goodId:* in goods)
                        {
                           if(count >= 50) break;
                           var good:* = goods[goodId];
                           if(good && good.hasOwnProperty("id"))
                           {
                              var goodName:String = good.hasOwnProperty("name") ? (" - " + good.name.v) : "";
                              output.push("  " + good.id.v + goodName);
                              count++;
                           }
                        }
                     }

                     output.push("道具总数: " + count);
                     foundData = true;
                     break;
                  }
               }
            }

            if(!foundData)
            {
               // 如果没找到，显示所有属性
               output.push("未找到标准道具属性，显示所有属性:");
               this.tryExportObjectProperties(goodConfig, "道具配置", output);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("道具配置导出失败: " + e.message + "\n");
         }
      }

      private function exportHeroConfig(output:Array) : void
      {
         try
         {
            var HeroConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.HeroConfig") as Class;
            var heroConfig:* = HeroConfig.instance();

            output.push("【英雄配置 - HeroConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // 使用已知的英雄ID来测试公共方法
            var knownHeroIds:Array = [1001, 1002, 1003, 1004, 1005]; // 孙悟空、小白龙、猪八戒、沙僧、小龙女

            if(heroConfig.hasOwnProperty("findRoleAsset"))
            {
               output.push("英雄资源配置 (通过findRoleAsset方法):");
               for each(var heroId:int in knownHeroIds)
               {
                  try
                  {
                     var asset:* = heroConfig.findRoleAsset(heroId);
                     if(asset)
                     {
                        var heroName:String = asset.hasOwnProperty("name") ? asset.name : "未知";
                        output.push("  " + heroId + " - " + heroName);
                        count++;
                        foundData = true;
                     }
                  }
                  catch(assetError:Error)
                  {
                     output.push("  " + heroId + " - 获取失败: " + assetError.message);
                  }
               }
            }

            if(heroConfig.hasOwnProperty("findConstRole"))
            {
               output.push("英雄常量配置 (通过findConstRole方法):");
               for each(var constId:int in knownHeroIds)
               {
                  try
                  {
                     var constData:* = heroConfig.findConstRole(constId);
                     if(constData)
                     {
                        var jobInfo:String = constData.hasOwnProperty("job") ? (" - 职业:" + constData.job.v) : "";
                        output.push("  " + constId + jobInfo);
                        foundData = true;
                     }
                  }
                  catch(constError:Error)
                  {
                     output.push("  " + constId + " - 获取失败: " + constError.message);
                  }
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(heroConfig, "英雄配置", output);
            }
            else
            {
               output.push("英雄总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("英雄配置导出失败: " + e.message + "\n");
         }
      }

      private function exportPetConfig(output:Array) : void
      {
         try
         {
            var PetConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.PetConfig") as Class;
            var petConfig:* = PetConfig.instance();

            output.push("【宠物配置 - PetConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // 使用已知的宠物ID来测试公共方法
            var knownPetIds:Array = [1101, 1102, 1103, 1104, 1106, 1107]; // 魔炎、蛇灵、金灵、水灵、冰麒麟、火凤凰

            if(petConfig.hasOwnProperty("findPetAsset"))
            {
               output.push("宠物资源配置 (通过findPetAsset方法):");
               for each(var petId:int in knownPetIds)
               {
                  try
                  {
                     var asset:* = petConfig.findPetAsset(petId);
                     if(asset)
                     {
                        var petName:String = asset.hasOwnProperty("name") ? asset.name : "未知";
                        output.push("  " + petId + " - " + petName);
                        count++;
                        foundData = true;
                     }
                  }
                  catch(assetError:Error)
                  {
                     output.push("  " + petId + " - 获取失败: " + assetError.message);
                  }
               }
            }

            if(petConfig.hasOwnProperty("findConstPet"))
            {
               output.push("宠物常量配置 (通过findConstPet方法):");
               for each(var constId:int in knownPetIds)
               {
                  try
                  {
                     var constData:* = petConfig.findConstPet(constId);
                     if(constData)
                     {
                        var wuXingInfo:String = constData.hasOwnProperty("wuXing") ? (" - 五行:" + constData.wuXing.v) : "";
                        output.push("  " + constId + wuXingInfo);
                        foundData = true;
                     }
                  }
                  catch(constError:Error)
                  {
                     output.push("  " + constId + " - 获取失败: " + constError.message);
                  }
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(petConfig, "宠物配置", output);
            }
            else
            {
               output.push("宠物总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("宠物配置导出失败: " + e.message + "\n");
         }
      }

      private function exportSkillConfig(output:Array) : void
      {
         try
         {
            var SkillConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.SkillConfig") as Class;
            var skillConfig:* = SkillConfig.instance();

            output.push("【技能配置 - SkillConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // 使用已知的技能ID来测试公共方法
            var knownSkillIds:Array = [
               1001, 1002, 1003, 1004, 1005, // 孙悟空技能
               1006, 1007, 1008, 1009, 1010, // 小白龙技能
               1011, 1012, 1013, 1014, 1015, // 猪八戒技能
               1016, 1017, 1018, 1019, 1020, // 沙僧技能
               1021, 1022, 1023, 1024, 1025  // 小龙女技能
            ];

            if(skillConfig.hasOwnProperty("findConstSkill"))
            {
               output.push("技能配置 (通过findConstSkill方法):");
               for each(var skillId:int in knownSkillIds)
               {
                  try
                  {
                     var skill:* = skillConfig.findConstSkill(skillId);
                     if(skill)
                     {
                        var skillName:String = skill.hasOwnProperty("name") ? skill.name : "未知";
                        var desc:String = skill.hasOwnProperty("infor") ? (" - " + skill.infor.substr(0, 30)) : "";
                        output.push("  " + skillId + " - " + skillName + desc);
                        count++;
                        foundData = true;
                     }
                  }
                  catch(skillError:Error)
                  {
                     // 忽略不存在的技能ID
                  }
               }
            }

            // 测试组合技能
            if(skillConfig.hasOwnProperty("findConstCombine"))
            {
               var combineIds:Array = [10001]; // 已知的组合技能ID
               output.push("组合技能配置 (通过findConstCombine方法):");
               for each(var combineId:int in combineIds)
               {
                  try
                  {
                     var combine:* = skillConfig.findConstCombine(combineId);
                     if(combine)
                     {
                        var combineName:String = combine.hasOwnProperty("name") ? combine.name : "未知";
                        output.push("  " + combineId + " - " + combineName);
                        count++;
                        foundData = true;
                     }
                  }
                  catch(combineError:Error)
                  {
                     output.push("  " + combineId + " - 获取失败: " + combineError.message);
                  }
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(skillConfig, "技能配置", output);
            }
            else
            {
               output.push("技能总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("技能配置导出失败: " + e.message + "\n");
         }
      }

      private function exportEquipConfig(output:Array) : void
      {
         try
         {
            var EquipConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.EquipConfig") as Class;
            var equipConfig:* = EquipConfig.instance();

            output.push("【装备配置 - EquipConfig】");
            this.tryExportObjectProperties(equipConfig, "装备配置", output);
         }
         catch(e:Error)
         {
            output.push("装备配置导出失败: " + e.message + "\n");
         }
      }

      private function exportMissionConfig(output:Array) : void
      {
         try
         {
            var MissionConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.MissionConfig") as Class;
            var missionConfig:* = MissionConfig.instance();

            output.push("【任务配置 - MissionConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // MissionConfig使用missionDic存储任务资源
            if(missionConfig.hasOwnProperty("missionDic"))
            {
               var missionDic:* = missionConfig.missionDic;
               if(missionDic)
               {
                  output.push("任务资源配置 (missionDic):");
                  for(var missionType:* in missionDic)
                  {
                     var resourceArray:Array = missionDic[missionType] as Array;
                     if(resourceArray && resourceArray.length > 0)
                     {
                        output.push("  任务类型: " + missionType + " (" + resourceArray.length + "个资源文件)");

                        for(var i:int = 0; i < resourceArray.length; i++)
                        {
                           output.push("    " + resourceArray[i]);
                           count++;
                        }


                     }
                  }
                  foundData = true;
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(missionConfig, "任务配置", output);
            }
            else
            {
               output.push("任务资源总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("任务配置导出失败: " + e.message + "\n");
         }
      }

      private function exportEnemyConfig(output:Array) : void
      {
         try
         {
            var EnemyConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.EnemyConfig") as Class;
            var enemyConfig:* = EnemyConfig.instance();

            output.push("【敌人配置 - EnemyConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // 使用已知的敌人ID范围来测试
            var knownEnemyIds:Array = [2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010]; // 从init方法中看到的ID

            if(enemyConfig.hasOwnProperty("findRoleAsset"))
            {
               output.push("敌人资源配置 (通过findRoleAsset方法):");
               for each(var enemyId:int in knownEnemyIds)
               {
                  try
                  {
                     var asset:* = enemyConfig.findRoleAsset(enemyId);
                     if(asset)
                     {
                        var enemyName:String = asset.hasOwnProperty("name") ? asset.name : "未知";
                        output.push("  " + enemyId + " - " + enemyName);
                        count++;
                        foundData = true;
                     }
                  }
                  catch(assetError:Error)
                  {
                     // 忽略不存在的敌人ID
                  }
               }
            }

            if(enemyConfig.hasOwnProperty("findConstRole"))
            {
               output.push("敌人常量配置 (通过findConstRole方法):");
               for each(var constId:int in knownEnemyIds)
               {
                  try
                  {
                     var constData:* = enemyConfig.findConstRole(constId);
                     if(constData)
                     {
                        var wuxingInfo:String = constData.hasOwnProperty("wuxing") ? (" - 五行:" + constData.wuxing.v) : "";
                        output.push("  " + constId + wuxingInfo);
                        foundData = true;
                     }
                  }
                  catch(constError:Error)
                  {
                     // 忽略不存在的敌人ID
                  }
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(enemyConfig, "敌人配置", output);
            }
            else
            {
               output.push("敌人总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("敌人配置导出失败: " + e.message + "\n");
         }
      }

      private function exportMallConfig(output:Array) : void
      {
         try
         {
            var MallConfig:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("file.MallConfig") as Class;
            var mallConfig:* = MallConfig.instance();

            output.push("【商城配置 - MallConfig】");

            var foundData:Boolean = false;
            var count:int = 0;

            // MallConfig使用_moneyList和_ticketList存储商城数据
            if(mallConfig.hasOwnProperty("_moneyList"))
            {
               var moneyList:* = mallConfig._moneyList;
               if(moneyList)
               {
                  output.push("金锭商城配置 (_moneyList):");
                  for(var moneyType:* in moneyList)
                  {
                     var moneyArray:Array = moneyList[moneyType] as Array;
                     if(moneyArray && moneyArray.length > 0)
                     {
                        output.push("  类型 " + moneyType + " (" + moneyArray.length + "个商品):");

                        for(var i:int = 0; i < moneyArray.length; i++)
                        {
                           var moneyItem:* = moneyArray[i];
                           if(moneyItem)
                           {
                              var itemInfo:String = this.getMallItemInfo(moneyItem);
                              output.push("    " + itemInfo);
                              count++;
                           }
                        }


                     }
                  }
                  foundData = true;
               }
            }

            if(mallConfig.hasOwnProperty("_ticketList"))
            {
               var ticketList:* = mallConfig._ticketList;
               if(ticketList)
               {
                  output.push("点券商城配置 (_ticketList):");
                  for(var ticketType:* in ticketList)
                  {
                     var ticketArray:Array = ticketList[ticketType] as Array;
                     if(ticketArray && ticketArray.length > 0)
                     {
                        output.push("  类型 " + ticketType + " (" + ticketArray.length + "个商品):");

                        for(var j:int = 0; j < ticketArray.length; j++)
                        {
                           var ticketItem:* = ticketArray[j];
                           if(ticketItem)
                           {
                              var ticketInfo:String = this.getMallItemInfo(ticketItem);
                              output.push("    " + ticketInfo);
                              count++;
                           }
                        }


                     }
                  }
                  foundData = true;
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(mallConfig, "商城配置", output);
            }
            else
            {
               output.push("商城商品总数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("商城配置导出失败: " + e.message + "\n");
         }
      }

      private function exportOtherConfigs(output:Array) : void
      {
         // 导出其他所有配置文件
         var configClasses:Array = [
            "file.BuffConfig",
            "file.BuyConfig",
            "file.ChenghaoConfig",
            "file.FlagConfig",
            "file.ForgeConfig",
            "file.GiftConfig",
            "file.KeyConfig",
            "file.NoticeConfig",
            "file.PetSkillConfig",
            "file.PetTalentConfig",
            "file.RankDicConfig",
            "file.SoundConfig"
         ];

         for each(var className:String in configClasses)
         {
            try
            {
               var ConfigClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition(className) as Class;
               var config:* = ConfigClass.instance();

               output.push("【" + className.split(".")[1] + "】");
               this.trySmartExport(config, className, output);
            }
            catch(e:Error)
            {
               output.push("【" + className + "】导出失败: " + e.message + "\n");
            }
         }
      }

      private function trySmartExport(config:*, configName:String, output:Array) : void
      {
         try
         {
            var foundData:Boolean = false;
            var count:int = 0;

            // 尝试查找常见的数组/列表属性
            var commonArrayProps:Array = ["fbList", "list", "_list", "flags", "_flags", "chenghaos", "_chenghaos"];

            for each(var propName:String in commonArrayProps)
            {
               if(config.hasOwnProperty(propName))
               {
                  try
                  {
                     var propValue:* = config[propName];
                     if(propValue is Array && propValue.length > 0)
                     {
                        output.push("找到数组属性: " + propName + " (长度:" + propValue.length + ")");

                        for(var i:int = 0; i < propValue.length; i++)
                        {
                           var item:* = propValue[i];
                           if(item)
                           {
                              var itemInfo:String = this.getItemInfo(item);
                              output.push("  [" + i + "] " + itemInfo);
                              count++;
                           }
                        }



                        foundData = true;
                        break;
                     }
                  }
                  catch(propError:Error)
                  {
                     output.push("属性 " + propName + " 访问失败: " + propError.message);
                  }
               }
            }

            if(!foundData)
            {
               this.tryExportObjectProperties(config, configName, output);
            }
            else
            {
               output.push("总计项目数: " + count);
               output.push("");
            }
         }
         catch(e:Error)
         {
            output.push("智能导出失败: " + e.message + "\n");
         }
      }

      private function getItemInfo(item:*) : String
      {
         try
         {
            var info:String = "";

            // 尝试获取ID
            if(item.hasOwnProperty("id") && item.id.hasOwnProperty("v"))
            {
               info += "ID:" + item.id.v;
            }
            else if(item.hasOwnProperty("fbID") && item.fbID.hasOwnProperty("v"))
            {
               info += "ID:" + item.fbID.v;
            }

            // 尝试获取名称
            if(item.hasOwnProperty("name"))
            {
               if(item.name && item.name.hasOwnProperty("v"))
               {
                  info += " - " + item.name.v;
               }
               else if(item.name)
               {
                  info += " - " + String(item.name);
               }
            }

            // 如果没有找到有用信息，显示对象类型
            if(info == "")
            {
               info = String(item).substr(0, 50);
            }

            return info;
         }
         catch(e:Error)
         {
            return "解析失败: " + e.message;
         }
      }

      private function tryExportObjectProperties(obj:*, configName:String, output:Array) : void
      {
         try
         {
            var count:int = 0;
            var hasData:Boolean = false;

            // 遍历对象的所有属性
            for(var prop:String in obj)
            {
               try
               {
                  var value:* = obj[prop];

                  // 检查是否是Vector类型
                  if(value && value.hasOwnProperty("length") && typeof(value.length) == "number")
                  {
                     var vecLength:int = value.length;
                     if(vecLength > 0)
                     {
                        output.push("属性 " + prop + " (Vector/Array，长度:" + vecLength + "):");
                        for(var i:int = 0; i < vecLength; i++)
                        {
                           var vecItem:* = value[i];
                           if(vecItem)
                           {
                              // 尝试获取ID和名称
                              var id:String = "";
                              var name:String = "";

                              if(vecItem.hasOwnProperty("id") && vecItem.id.hasOwnProperty("v"))
                              {
                                 id = vecItem.id.v;
                              }
                              else if(vecItem.hasOwnProperty("roleID") && vecItem.roleID.hasOwnProperty("v"))
                              {
                                 id = vecItem.roleID.v;
                              }
                              else if(vecItem.hasOwnProperty("petID") && vecItem.petID.hasOwnProperty("v"))
                              {
                                 id = vecItem.petID.v;
                              }
                              else if(vecItem.hasOwnProperty("skillID") && vecItem.skillID.hasOwnProperty("v"))
                              {
                                 id = vecItem.skillID.v;
                              }

                              if(vecItem.hasOwnProperty("name"))
                              {
                                 if(vecItem.name.hasOwnProperty("v"))
                                 {
                                    name = " - " + vecItem.name.v;
                                 }
                                 else
                                 {
                                    name = " - " + vecItem.name;
                                 }
                              }
                              else if(vecItem.hasOwnProperty("skillName"))
                              {
                                 if(vecItem.skillName.hasOwnProperty("v"))
                                 {
                                    name = " - " + vecItem.skillName.v;
                                 }
                                 else
                                 {
                                    name = " - " + vecItem.skillName;
                                 }
                              }

                              if(id != "")
                              {
                                 output.push("  " + id + name);
                                 count++;
                              }
                              else
                              {
                                 output.push("  [" + i + "] " + String(vecItem).substr(0, 50));
                              }
                           }
                        }

                        hasData = true;
                     }
                  }
                  // 检查Dictionary类型
                  else if(value is Object && value !== null && !(value is String) && !(value is Number) && !(value is Boolean))
                  {
                     var objCount:int = 0;
                     for(var subProp:String in value)
                     {
                        objCount++;

                     }

                     if(objCount > 0)
                     {
                        output.push("属性 " + prop + " (Dictionary/Object，包含" + objCount + "个子项):");
                        var itemCount:int = 0;
                        for(var key:String in value)
                        {


                           var item:* = value[key];
                           if(item is Array)
                           {
                              output.push("  " + key + ": Array[" + item.length + "]");
                           }
                           else if(item && item.hasOwnProperty("id"))
                           {
                              var itemName:String = "";
                              if(item.hasOwnProperty("name") && item.name.hasOwnProperty("v"))
                              {
                                 itemName = " - " + item.name.v;
                              }
                              output.push("  " + key + ": " + item.id.v + itemName);
                              count++;
                           }
                           else
                           {
                              output.push("  " + key + ": " + String(item).substr(0, 40));
                           }
                           itemCount++;
                        }

                        hasData = true;
                     }
                  }
                  else if(value !== null && value !== undefined)
                  {
                     // 简单类型的值
                     var valueStr:String = String(value);
                     if(valueStr.length > 0 && valueStr != "[object Object]")
                     {
                        output.push("属性 " + prop + ": " + valueStr.substr(0, 80));
                        hasData = true;
                     }
                  }
               }
               catch(propError:Error)
               {
                  // 忽略单个属性的错误
                  output.push("属性 " + prop + ": 访问失败 - " + propError.message);
               }
            }

            if(!hasData)
            {
               output.push("未找到可导出的数据结构");
            }
            else
            {
               output.push("总计项目数: " + count);
            }

            output.push("");
         }
         catch(e:Error)
         {
            output.push("属性遍历失败: " + e.message + "\n");
         }
      }

      private function getMallItemInfo(mallItem:*) : String
      {
         try
         {
            var info:String = "";

            // 尝试获取价格
            if(mallItem.hasOwnProperty("price") && mallItem.price.hasOwnProperty("v"))
            {
               info += "价格:" + mallItem.price.v;
            }

            // 尝试获取商品信息
            if(mallItem.hasOwnProperty("mallGood"))
            {
               var mallGood:* = mallItem.mallGood;
               if(mallGood && mallGood.hasOwnProperty("id") && mallGood.id.hasOwnProperty("v"))
               {
                  info += " 商品ID:" + mallGood.id.v;
               }
            }

            // 尝试获取奖励信息
            if(mallItem.hasOwnProperty("reward"))
            {
               var reward:* = mallItem.reward;
               if(reward && reward.hasOwnProperty("id") && reward.id.hasOwnProperty("v"))
               {
                  info += " 奖励ID:" + reward.id.v;
               }
            }

            if(info == "")
            {
               info = String(mallItem).substr(0, 50);
            }

            return info;
         }
         catch(e:Error)
         {
            return "解析失败: " + e.message;
         }
      }
   }
}

